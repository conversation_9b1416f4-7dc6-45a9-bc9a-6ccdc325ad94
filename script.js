'use strict';

const game = {
  team1: 'Bayern Munich',
  team2: 'Borrussia Dortmund',
  players: [
    [
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON>',
    ],
    [
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
    ],
  ],
  score: '4:0',
  scored: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
  date: 'Nov 9th, 2037',
  odds: {
    team1: 1.33,
    x: 3.25,
    team2: 6.5,
  },
};

// Data needed for a later exercise
const flights =
  '_Delayed_Departure;fao93766109;txl2133758440;11:25+_Arrival;bru0943384722;fao93766109;11:45+_Delayed_Arrival;hel7439299980;fao93766109;12:05+_Departure;fao93766109;lis2323639855;12:30';

const italianFoods = new Set([
  'pasta',
  'gnocchi',
  'tomatoes',
  'olive oil',
  'garlic',
  'basil',
]);

const mexicanFoods = new Set([
  'tortillas',
  'beans',
  'rice',
  'tomatoes',
  'avocado',
  'garlic',
]);

const openingHours = {
  thu: {
    open: 12,
    close: 22,
  },
  fri: {
    open: 11,
    close: 23,
  },
  sat: {
    open: 0, // Open 24 hours
    close: 24,
    close: 24,
  },
};

// Data needed for first part of the section
const restaurant = {
  name: 'Classico Italiano',
  location: 'Via Angelo Tavanti 23, Firenze, Italy',
  categories: ['Italian', 'Pizzeria', 'Vegetarian', 'Organic'],
  starterMenu: ['Focaccia', 'Bruschetta', 'Garlic Bread', 'Caprese Salad'],
  mainMenu: ['Pizza', 'Pasta', 'Risotto'],

  order: function (starterIndex, mainIndex) {
    return [this.starterMenu[starterIndex], this.mainMenu[mainIndex]];
  },

  orderDeliver: function ({
    starterIndex = 1,
    mainIndex = 0,
    time = '20:00',
    address,
  }) {
    console.log(` Order received! ${this.starterMenu[starterIndex]}
      and ${this.mainMenu[mainIndex]}
      will be delivered to ${address} at ${time}`);
  },

  orderPasta: function ({ ing1, ing2, ing3 }) {
    console.log(
      `Here is your delicious pasta with ${ing1},${ing2} and ${ing3}`
    );
  },

  orderPizza: function (mainIngr, ...otherIngr) {
    console.log(mainIngr);
    console.log(otherIngr);
  },

  openingHours,
};

///////////////////////////////////////
// Coding Challenge #4

/* Write a program that receives a list of variable names written in underscore_case and convert them to camelCase.

The input will come from a textarea inserted into the DOM (see code below), and conversion will happen when the button is pressed.

THIS TEST DATA (pasted to textarea)
underscore_case
 first_name
Some_Variable 
  calculate_AGE
delayed_departure

SHOULD PRODUCE THIS OUTPUT (5 separate console.log outputs)
underscoreCase      ✅
firstName           ✅✅
someVariable        ✅✅✅
calculateAge        ✅✅✅✅
delayedDeparture    ✅✅✅✅✅

HINT 1: Remember which character defines a new line in the textarea 😉
HINT 2: The solution only needs to work for a variable made out of 2 words, like a_b
HINT 3: Start without worrying about the ✅. Tackle that only after you have the variable name conversion working 😉
HINT 4: This challenge is difficult on purpose, so start watching the solution in case you're stuck. Then pause and continue!

Afterwards, test with your own test data!

GOOD LUCK 😀
*/
const textArea = document.createElement('textarea');
const btn = document.createElement('button');
b
const body = document.querySelector('body');
body.appendChild(textArea);
body.appendChild(btn);
console.log(body);

///////////////////////////////////////
// Coding Challenge #3

/* 
Let's continue with our football betting app! This time, we have a map with a log of the events that happened during the game. The values are the events themselves, and the keys are the minutes in which each event happened (a football game has 90 minutes plus some extra time).

1. Create an array 'events' of the different game events that happened (no duplicates)
2. After the game has finished, is was found that the yellow card from minute 64 was unfair. So remove this event from the game events log.
3. Print the following string to the console: "An event happened, on average, every 9 minutes" (keep in mind that a game has 90 minutes)
4. Loop over the events and log them to the console, marking whether it's in the first half or second half (after 45 min) of the game, like this:
      [FIRST HALF] 17: ⚽️ GOAL

GOOD LUCK 😀
*/

const gameEvents = new Map([
  [17, '⚽️ GOAL'],
  [36, '🔁 Substitution'],
  [47, '⚽️ GOAL'],
  [61, '🔁 Substitution'],
  [64, '🔶 Yellow card'],
  [69, '🔴 Red card'],
  [70, '🔁 Substitution'],
  [72, '🔁 Substitution'],
  [76, '⚽️ GOAL'],
  [80, '⚽️ GOAL'],
  [92, '🔶 Yellow card'],
]);

// // 1.
// const events = [...new Set(gameEvents.values())];
// console.log(events);

// // 2.
// gameEvents.delete(64);
// console.log(gameEvents);

// // 3.
// console.log(
//   `An event happened, on average, every ${90 / gameEvents.size} minutes`
// );

// // 4.
// for (const [min, event] of gameEvents) {
//   const half = min <= 45 ? 'FIRST HALF' : 'SECOND HALF';
//   console.log(`[${half}] ${min}: ${event}`);
// }

// const question = new Map([
//   ['question', 'What is the best programming language?'],
//   [1, 'C'],
//   [2, 'Java'],
//   [3, 'Python'],
//   [4, 'JavaScript'],
//   ['correct', 4],
//   [true, 'Correct!!!'],
//   [false, 'Try again!'],
// ]);

// console.log(question.get('question'));
// for (const [key, value] of question) {
//   if (typeof key === 'number') console.log(`Answer ${key}: ${value}`);
// }

// const answer = Number(prompt('Your answer'));
// console.log(question.get(answer === question.get('correct')));

// console.log([...question]);
// console.log([...question.entries()]);
// console.log([...question.keys()]);
// console.log([...question.values()]);

// const arr = [2, 3, 4];
// const a = arr[0];
// const b = arr[1];
// const c = arr[2];

// const [x, y, z] = arr;
// console.log(a, b, c);
// console.log(x, y, z);

// let [main, , secondary] = restaurant.categories;

// console.log(main, secondary);
// [main, secondary] = [secondary, main];
// console.log(main, secondary);

// const [starter, mainCourse] = restaurant.order(2, 0);

// console.log(starter, mainCourse);

//const { name, openingHours, categories } = restaurant;

// restaurant.orderDeliver({
//   time: '22:30',
//   adrress: ' Via del Sole, 21',
//   mainIndex: 2,
//   startIndex: 2,
// });

//Spread operator
// const arr = [7, 8, 9];
// const badNewArr = [1, 2, arr[0], arr[1], arr[2]];
// console.log(badNewArr);

// const newArr = [1, 2, ...arr];
// console.log(newArr);

// console.log(...newArr);

// const newMenu = [...restaurant.mainMenu, 'Gnocci'];
// console.log(newMenu);

// //Copy array
// const mainMenuCopy = [...restaurant.mainMenu];

// //Join 2 arrays
// const merge = [...restaurant.starterMenu, ...restaurant.mainMenu];
// console.log(merge);

// const ingredients = [
//   prompt(
//     "Let's make pasta! Ingredient 1?",
//     "Let's make pasta! Ingredient 2?",
//     "Let's make pasta! Ingredient 3?"
//   ),
// ];

// restaurant.orderPasta(...ingredients);

// // Objects

// const newRestaurant = { ...restaurant };
// newRestaurant.name = 'New Classico Italiano';
// console.log(newRestaurant.name);

//1) Destructuring

// Spread, because on right side og =
// const arr = [1, 2, ...[3, 4]];

// // Rest, because on left side of =
// const [a, b, ...others] = [1, 2, 3, 4, 5];

// const [pizza, , risotto, ...otherFood] = [
//   ...restaurant.mainMenu,
//   ...restaurant.starterMenu,
// ];

// console.log(pizza, risotto, otherFood);

// const { sat, ...weekdays } = restaurant.openingHours;

// console.log(weekdays);

// // 2) Functions
// const add = (...numbers) => {};
// add(2, 3);
// add(4, 5, 6, 7);
// add(8, 9, 10, 1, 2, 3, 4);
// add(5, 6, 7, 8, 9, 0, 10, 11, 12, 13);

// console.log('0' || 'Vlad'); // 0
// console.log('' || '0'); // 0
// console.log('0' - 1 || 'Vlad'); // -1
// console.log(0 || 'Vlad'); // Vlad
// console.log(0 || undefined); // undefined

// console.log('0' && 'Vlad'); // 'Vlad'
// console.log('0' - 1 && 'Vlad'); // 'Vlad'
// console.log('Vlad' && {}); // {}
// console.log(0 && 'Vlad'); // 0
// console.log(0 && undefined); // 0
// restaurant.numGuests = ' a';
// console.log(0 && restaurant.numGuests); // 0
// console.log(restaurant.numGuests && 1 > 3); //0
// const newGuests = restaurant.numGuests ?? 10;
// console.log(newGuests);

///////////////////////////////////////
// Coding Challenge #1

/* 
We're building a football betting app (soccer for my American friends 😅)!

Suppose we get data from a web service about a certain game (below). In this challenge we're gonna work with the data. So here are your tasks:

1. Create one player array for each team (variables 'players1' and 'players2')
2. The first player in any player array is the goalkeeper and the others are field players. For Bayern Munich (team 1) create one variable ('gk') with the goalkeeper's name, and one array ('fieldPlayers') with all the remaining 10 field players
3. Create an array 'allPlayers' containing all players of both teams (22 players)
4. During the game, Bayern Munich (team 1) used 3 substitute players. So create a new array ('players1Final') containing all the original team1 players plus 'Thiago', 'Coutinho' and 'Perisic'
5. Based on the game.odds object, create one variable for each odd (called 'team1', 'draw' and 'team2')
6. Write a function ('printGoals') that receives an arbitrary number of player names (NOT an array) and prints each of them to the console, along with the number of goals that were scored in total (number of player names passed in)
7. The team with the lower odd is more likely to win. Print to the console which team is more likely to win, WITHOUT using an if/else statement or the ternary operator.

TEST DATA FOR 6: Use players 'Davies', 'Muller', 'Lewandowski' and 'Kimmich'. Then, call the function again with players from game.scored

GOOD LUCK 😀
*/

// // 1.
// const players1 = [...game.players[0]];
// const players2 = [...game.players[1]];

// console.log('Players 1: ', players1);
// console.log('Players 2: ', players2);

// // 2.
// const [gk, ...fieldPlayers] = [...game.players[0]];

// console.log('goalkeeper: ', gk);
// console.log('Field players: ', fieldPlayers);

// // 3.
// const allPlayers = [...game.players[0], ...game.players[1]];
// console.log('All players:', allPlayers);

// // 4.
// const players1Final = [...players1, 'Thiago', 'Coutinho', 'Perisic'];
// console.log(players1Final);

// // 5.
// const {
//   odds: { team1, x: draw, team2 },
// } = game;
// console.log(team1, draw, team2);

// // 6.
// const printGoals = (...names) => {
//   for (let i = 0; i < names.length; i++) {
//     console.log(names[i]);
//   }
// };

// printGoals('Davies', 'Muller', 'Lewandowski', 'Kimmich');
